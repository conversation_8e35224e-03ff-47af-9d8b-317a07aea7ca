/**
 * WCAG Database Service
 * Database operations for WCAG compliance data
 * Following HIPAA/GDPR patterns with real database operations using Knex ORM
 */

import db from '../../../lib/db';
import { v4 as uuidv4 } from 'uuid';
import { Knex } from 'knex';
import { WcagScanR<PERSON>ult, WcagScanConfig, WcagRuleResult, ScanStatus, RiskLevel } from '../types';

export interface ScanListOptions {
  page: number;
  limit: number;
  status?: ScanStatus;
  startDate?: Date;
  endDate?: Date;
  sortBy: 'scanTimestamp' | 'overallScore' | 'targetUrl';
  sortOrder: 'asc' | 'desc';
}

export interface ScanListResult {
  scans: Array<{
    scanId: string;
    targetUrl: string;
    status: ScanStatus;
    overallScore?: number;
    levelAchieved?: 'A' | 'AA' | 'AAA' | 'FAIL';
    riskLevel?: RiskLevel;
    scanTimestamp: string;
    completionTimestamp?: string;
    totalAutomatedChecks?: number;
    passedAutomatedChecks?: number;
    failedAutomatedChecks?: number;
    manualReviewItems?: number; // Count only, no scoring
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface WcagScanEntity {
  id: string;
  user_id: string;
  target_url: string;
  scan_timestamp: Date;
  scan_duration?: number;
  overall_score?: number;
  scan_status: ScanStatus;
  completion_timestamp?: Date;
  total_rules: number;
  passed_rules: number;
  failed_rules: number;
  risk_level?: RiskLevel;
  wcag_version: string;
  conformance_level: string;
  metadata?: any;
}

export interface WcagAutomatedResultEntity {
  id: string;
  scan_id: string;
  rule_id: string;
  rule_name: string;
  category: string;
  wcag_version: string;
  conformance_level: string;
  automation_level: number;
  status: 'passed' | 'failed' | 'not_applicable';
  score: number;
  max_score: number;
  execution_time: number;
  details?: any;
  recommendations?: any;
  created_at: Date;
}

export class WcagDatabase {
  /**
   * Create a new WCAG scan record
   */
  static async createScan(config: WcagScanConfig): Promise<string> {
    try {
      console.log('📝 Creating WCAG scan record...');

      const insertData: any = {
        user_id: config.userId,
        target_url: config.targetUrl,
        scan_timestamp: new Date(),
        total_rules: 0,
        passed_rules: 0,
        failed_rules: 0,
        scan_status: 'pending',
        wcag_version: config.scanOptions?.wcagVersion || '2.1',
        conformance_level: config.scanOptions?.level || 'AA',
        metadata: config.scanOptions,
      };

      // Use provided scanId if available, otherwise let database generate one
      if (config.requestId) {
        insertData.id = config.requestId;
      }

      const [scanId] = await db('wcag_scans').insert(insertData).returning('id');
      const finalScanId = typeof scanId === 'object' ? scanId.id : scanId;

      console.log(`✅ WCAG scan record created with ID: ${finalScanId}`);
      return finalScanId;
    } catch (error) {
      console.error('❌ Error creating WCAG scan record:', error);
      throw new Error(`Failed to create WCAG scan record: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get user's WCAG scans with pagination and filtering
   */
  async getUserScans(userId: string, options: ScanListOptions): Promise<ScanListResult> {
    try {
      console.log(`📊 Fetching WCAG scans for user: ${userId}`, options);

      let query = db('wcag_scans')
        .where('user_id', userId)
        .select('*');

      // Apply filters
      if (options.status) {
        query = query.where('scan_status', options.status);
      }

      if (options.startDate) {
        query = query.where('scan_timestamp', '>=', options.startDate);
      }

      if (options.endDate) {
        query = query.where('scan_timestamp', '<=', options.endDate);
      }

      // Apply sorting
      const sortColumn = options.sortBy === 'scanTimestamp' ? 'scan_timestamp' :
                        options.sortBy === 'overallScore' ? 'overall_score' :
                        'target_url';
      query = query.orderBy(sortColumn, options.sortOrder);

      // Get total count for pagination
      const totalQuery = query.clone().clearSelect().count('* as count');
      const [{ count }] = await totalQuery;
      const total = parseInt(count as string, 10);

      // Apply pagination
      const offset = (options.page - 1) * options.limit;
      const scans = await query.limit(options.limit).offset(offset);

      // Transform to expected format
      const transformedScans = scans.map((scan: WcagScanEntity) => ({
        scanId: scan.id,
        targetUrl: scan.target_url,
        status: scan.scan_status,
        overallScore: scan.overall_score,
        levelAchieved: this.calculateLevelAchieved(scan.overall_score),
        riskLevel: scan.risk_level,
        scanTimestamp: scan.scan_timestamp.toISOString(),
        completionTimestamp: scan.completion_timestamp?.toISOString(),
        totalAutomatedChecks: scan.total_rules,
        passedAutomatedChecks: scan.passed_rules,
        failedAutomatedChecks: scan.failed_rules,
        manualReviewItems: 0 // Will be calculated from manual_reviews table
      }));

      return {
        scans: transformedScans,
        pagination: {
          page: options.page,
          limit: options.limit,
          total,
          totalPages: Math.ceil(total / options.limit)
        }
      };
    } catch (error) {
      console.error('❌ Error fetching WCAG scans:', error);
      throw new Error(`Failed to fetch WCAG scans: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get scan by ID for specific user
   */
  async getScanById(scanId: string, userId: string): Promise<WcagScanResult | null> {
    try {
      console.log(`🔍 Fetching WCAG scan: ${scanId} for user: ${userId}`);

      const scan = await db('wcag_scans')
        .where({ id: scanId, user_id: userId })
        .first();

      if (!scan) {
        return null;
      }

      // Get automated results
      const automatedResults = await db('wcag_automated_results')
        .where('scan_id', scanId)
        .select('*');

      // Transform to WcagScanResult format
      const ruleResults: WcagRuleResult[] = automatedResults.map((result: WcagAutomatedResultEntity) => ({
        ruleId: result.rule_id,
        ruleName: result.rule_name,
        category: result.category as any,
        wcagVersion: result.wcag_version as any,
        successCriterion: '', // Not available in entity, will be populated from rule config
        level: result.conformance_level as any,
        status: result.status,
        score: result.score,
        maxScore: result.max_score,
        weight: 1.0, // Default weight
        automated: true, // These are automated results
        evidence: [], // Not stored in entity, would need separate table
        recommendations: result.recommendations || [],
        executionTime: result.execution_time,
        errorMessage: undefined, // Not available in entity
        manualReviewItems: [] // Not stored in entity, would need separate table
      }));

      // Build scan result
      const scanResult: WcagScanResult = {
        scanId: scan.id,
        targetUrl: scan.target_url,
        config: {
          targetUrl: scan.target_url,
          userId: scan.user_id,
          requestId: scan.id,
          scanOptions: scan.metadata
        },
        summary: {
          overallScore: scan.overall_score || 0,
          overallScore: scan.overall_score || 0,
          riskLevel: scan.risk_level || 'medium',
          totalRules: scan.total_rules,
          passedRules: scan.passed_rules,
          failedRules: scan.failed_rules,
          notApplicableRules: 0,
          averageAutomation: 0.87,
          scanDuration: scan.scan_duration || 0,
          categoryBreakdown: {},
          versionBreakdown: {},
          levelBreakdown: {}
        },
        ruleResults,
        scanProgress: {
          scanId: scan.id,
          status: scan.scan_status === 'completed' ? 'completed' : 'running',
          currentRule: undefined,
          completedRules: ruleResults.map(r => r.ruleId),
          totalRules: scan.total_rules,
          progress: 100,
          startTime: scan.scan_timestamp,
          lastUpdate: scan.completion_timestamp || scan.scan_timestamp
        },
        createdAt: scan.scan_timestamp,
        completedAt: scan.completion_timestamp
      };

      return scanResult;
    } catch (error) {
      console.error('❌ Error fetching WCAG scan:', error);
      throw new Error(`Failed to fetch WCAG scan: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete scan by ID for specific user
   */
  async deleteScan(scanId: string, userId: string): Promise<boolean> {
    try {
      console.log(`🗑️ Deleting WCAG scan: ${scanId} for user: ${userId}`);

      const result = await db('wcag_scans')
        .where({ id: scanId, user_id: userId })
        .del();

      return result > 0;
    } catch (error) {
      console.error('❌ Error deleting WCAG scan:', error);
      throw new Error(`Failed to delete WCAG scan: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Health check for database connectivity
   */
  async healthCheck(): Promise<boolean> {
    try {
      console.log('🏥 Checking WCAG database health');

      // Test basic database connectivity
      await db.raw('SELECT 1');

      // Check if WCAG tables exist
      const tables = ['wcag_scans', 'wcag_automated_results'];
      for (const table of tables) {
        const exists = await db.schema.hasTable(table);
        if (!exists) {
          console.error(`❌ Table ${table} does not exist`);
          return false;
        }
      }

      console.log('✅ WCAG database health check passed');
      return true;
    } catch (error) {
      console.error('❌ WCAG database health check failed:', error);
      return false;
    }
  }

  /**
   * Save scan result to database
   */
  async saveScanResult(scanResult: WcagScanResult): Promise<void> {
    try {
      console.log(`💾 Saving WCAG scan result: ${scanResult.scanId}`);

      // Update main scan record
      await db('wcag_scans')
        .where('id', scanResult.scanId)
        .update({
          overall_score: scanResult.summary.overallScore,
          scan_status: 'completed',
          completion_timestamp: new Date(),
          total_rules: scanResult.summary.totalRules,
          passed_rules: scanResult.summary.passedRules,
          failed_rules: scanResult.summary.failedRules,
          risk_level: scanResult.summary.riskLevel,
          scan_duration: scanResult.summary.scanDuration
        });

      // Save automated results
      if (scanResult.ruleResults && scanResult.ruleResults.length > 0) {
        const automatedResults = scanResult.ruleResults.map(result => ({
          id: uuidv4(),
          scan_id: scanResult.scanId,
          rule_id: result.ruleId,
          rule_name: result.ruleName,
          category: result.category,
          wcag_version: result.version,
          conformance_level: result.level,
          automation_level: result.automationLevel,
          status: result.status,
          score: result.score,
          max_score: result.maxScore,
          execution_time: result.executionTime,
          details: result.details,
          recommendations: result.recommendations,
          created_at: new Date()
        }));

        await db('wcag_automated_results').insert(automatedResults);
      }

      console.log(`✅ WCAG scan result saved: ${scanResult.scanId}`);
    } catch (error) {
      console.error('❌ Error saving WCAG scan result:', error);
      throw new Error(`Failed to save WCAG scan result: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update scan status
   */
  async updateScanStatus(scanId: string, status: ScanStatus, errorMessage?: string): Promise<void> {
    try {
      console.log(`📝 Updating WCAG scan status: ${scanId} -> ${status}`);

      const updateData: any = {
        scan_status: status
      };

      if (status === 'failed' && errorMessage) {
        updateData.metadata = db.raw('COALESCE(metadata, \'{}\') || ?', [JSON.stringify({ error: errorMessage })]);
      }

      if (status === 'completed') {
        updateData.completion_timestamp = new Date();
      }

      await db('wcag_scans')
        .where('id', scanId)
        .update(updateData);

      console.log(`✅ WCAG scan status updated: ${scanId} -> ${status}`);
    } catch (error) {
      console.error('❌ Error updating WCAG scan status:', error);
      throw new Error(`Failed to update WCAG scan status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get scan progress (placeholder for orchestrator compatibility)
   */
  async getScanProgress(scanId: string): Promise<any> {
    // This would typically be stored in a separate progress table or cache
    // For now, return basic scan information
    try {
      const scan = await this.getScanById(scanId);
      return {
        scanId,
        status: scan?.status || 'unknown',
        progress: scan?.status === 'completed' ? 100 : 0
      };
    } catch (error) {
      console.error(`❌ Error getting scan progress:`, error);
      return null;
    }
  }

  /**
   * Store scan progress (placeholder for orchestrator compatibility)
   */
  async storeScanProgress(progress: any): Promise<void> {
    // This would typically store progress in a separate table or cache
    // For now, just update the scan status
    try {
      if (progress.scanId && progress.status) {
        await this.updateScanStatus(progress.scanId, progress.status);
      }
    } catch (error) {
      console.error(`❌ Error storing scan progress:`, error);
    }
  }

  /**
   * Update scan progress (placeholder for orchestrator compatibility)
   */
  async updateScanProgress(progress: any): Promise<void> {
    // This would typically update progress in a separate table or cache
    // For now, just update the scan status
    try {
      if (progress.scanId && progress.status) {
        await this.updateScanStatus(progress.scanId, progress.status);
      }
    } catch (error) {
      console.error(`❌ Error updating scan progress:`, error);
    }
  }

  /**
   * Helper method to calculate level achieved based on score
   */
  private calculateLevelAchieved(score?: number): 'A' | 'AA' | 'AAA' | 'FAIL' {
    if (!score) return 'FAIL';
    if (score >= 95) return 'AAA';
    if (score >= 80) return 'AA';
    if (score >= 60) return 'A';
    return 'FAIL';
  }
}
